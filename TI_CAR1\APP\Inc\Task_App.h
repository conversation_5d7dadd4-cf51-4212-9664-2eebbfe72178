#ifndef __Task_App_h
#define __Task_App_h

#include "SysConfig.h"

// 电机相关变量
extern int16_t Data_MotorEncoder[2];
extern _iq Data_Motor_TarSpeed;
extern MOTOR_Def_t *Motor[2];

// 循迹相关变量
extern _iq Data_Tracker_Offset;
extern uint8_t Data_Tracker_Input[8];

// 姿态数据
extern float Data_Pitch, Data_Roll, Data_Yaw;

// 灰度传感器数据
extern unsigned short Gray_Anolog[8];
extern unsigned short Gray_Normal[8];
extern unsigned char Gray_Digtal;

void Task_Init(void);

#endif