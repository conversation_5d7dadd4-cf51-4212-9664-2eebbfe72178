# 电机低速运行功能测试验证步骤

## 测试前准备

### 硬件检查
1. 确认电机连接正确（左电机连接到PWM通道0，右电机连接到PWM通道1）
2. 确认TB6612电机驱动器供电正常
3. 确认编码器连接正确并供电
4. 确认OLED显示屏I2C连接正常
5. 确认按键S2、K1、K2连接正常

### 软件准备
1. 编译修改后的代码
2. 确保没有编译错误
3. 准备调试工具（串口调试助手等）

## 测试步骤

### 第一步：上电基本功能测试

1. **上电观察**
   - 给系统上电
   - 观察OLED是否正常显示
   - 预期结果：OLED显示4行信息，第1行显示 `L:0.0 R:0.0`（初始速度）

2. **电机启动观察**
   - 上电后等待2-3秒（系统初始化完成）
   - 观察电机是否开始转动
   - 预期结果：左右电机应该开始低速转动

3. **OLED显示验证**
   - 观察OLED第2行是否显示 `Tar:10.0 Off:0.0`
   - 观察第1行速度数值是否开始变化
   - 预期结果：目标速度显示10.0，实际速度逐渐接近目标值

### 第二步：速度控制测试

1. **速度增加测试**
   - 按下S2按键
   - 观察OLED第2行目标速度是否变为15.0
   - 观察红色LED是否闪烁
   - 观察电机转速是否增加
   - 预期结果：目标速度+5，电机转速增加

2. **速度减少测试**
   - 按下K1按键
   - 观察OLED第2行目标速度是否减少5.0
   - 观察蓝色LED是否闪烁
   - 观察电机转速是否减少
   - 预期结果：目标速度-5，电机转速减少

3. **速度重置测试**
   - 按下K2按键
   - 观察OLED第2行目标速度是否重置为10.0
   - 观察板载LED状态是否切换
   - 预期结果：目标速度重置为10.0

4. **边界值测试**
   - 连续按S2直到速度达到50.0，再按一次确认不会超过50.0
   - 连续按K1直到速度达到0.0，再按一次确认不会低于0.0

### 第三步：实时显示测试

1. **速度显示精度测试**
   - 观察OLED第1行左右电机速度显示
   - 确认数值更新频率约为20Hz（每50ms更新）
   - 确认显示精度为小数点后1位

2. **时间显示测试**
   - 观察OLED第4行时间显示
   - 确认时间每秒递增1
   - 运行1分钟，确认显示为60s左右

3. **姿态显示测试**
   - 轻微倾斜小车
   - 观察OLED第3行姿态角度变化
   - 确认P（俯仰）、R（横滚）、Y（偏航）数值变化合理

### 第四步：PID控制测试

1. **速度跟踪测试**
   - 设置目标速度为20.0
   - 观察实际速度是否能稳定跟踪目标速度
   - 预期结果：实际速度应在目标速度±2范围内稳定

2. **负载扰动测试**
   - 轻微阻挡电机转动
   - 观察PID控制器是否能快速恢复到目标速度
   - 预期结果：扰动消除后2-3秒内恢复正常

### 第五步：长时间稳定性测试

1. **连续运行测试**
   - 让系统连续运行30分钟
   - 每5分钟记录一次速度显示值
   - 确认系统稳定运行，无异常重启

2. **温度稳定性测试**
   - 观察长时间运行后电机温度
   - 确认温度在合理范围内
   - 观察速度控制精度是否受温度影响

## 预期测试结果

### 正常结果
- 上电后电机立即开始低速转动（目标速度10）
- OLED正确显示所有信息，更新频率正常
- 按键控制响应及时，速度调节准确
- PID控制稳定，速度跟踪精度高
- 系统长时间稳定运行

### 异常情况处理
- **电机不转**：检查PWM输出、电机驱动器、电源
- **速度显示为0**：检查编码器连接、中断配置
- **按键无响应**：检查按键硬件、消抖逻辑
- **OLED显示异常**：检查I2C连接、显示初始化
- **速度控制不稳定**：调整PID参数

## 测试数据记录表

| 测试项目 | 预期结果 | 实际结果 | 通过/失败 | 备注 |
|---------|---------|---------|----------|------|
| 上电电机启动 | 目标速度10，电机转动 | | | |
| S2按键加速 | 速度+5，红灯闪烁 | | | |
| K1按键减速 | 速度-5，蓝灯闪烁 | | | |
| K2按键重置 | 速度重置为10 | | | |
| OLED显示更新 | 50ms更新周期 | | | |
| PID速度跟踪 | ±2精度范围 | | | |
| 长时间稳定性 | 30分钟无异常 | | | |

## 测试完成标准

- [ ] 所有基本功能测试通过
- [ ] 按键控制功能正常
- [ ] OLED显示准确及时
- [ ] PID控制稳定可靠
- [ ] 长时间运行稳定
- [ ] 无明显异常或错误

测试完成后，系统即可投入正常使用。
