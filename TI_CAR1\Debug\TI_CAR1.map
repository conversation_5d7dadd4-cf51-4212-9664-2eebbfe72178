******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 22:52:39 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00007855


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00009d48  000162b8  R  X
  SRAM                  20200000   00008000  00000706  000078fa  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00009d48   00009d48    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00008520   00008520    r-x .text
  000085e0    000085e0    000016f0   000016f0    r-- .rodata
  00009cd0    00009cd0    00000078   00000078    r-- .cinit
20200000    20200000    00000507   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    00000133   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00008520     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001364    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000159c    0000022c     MPU6050.o (.text.Read_Quad)
                  000017c8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000019f4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001c14    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001e08    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001fe8    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000021c4    000001b0     Task.o (.text.Task_Start)
                  00002374    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002514    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000026a6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000026a8    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00002830    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000029b8    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002b30    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002ca0    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002de4    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002f20    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00003054    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003188    00000130     OLED.o (.text.OLED_ShowChar)
                  000032b8    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000033e8    00000128     Task_App.o (.text.Task_Tracker)
                  00003510    00000128     inv_mpu.o (.text.mpu_init)
                  00003638    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  0000375c    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003880    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  000039a0    00000110     OLED.o (.text.OLED_Init)
                  00003ab0    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003bbc    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003cc4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003dc8    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00003ec8    000000fc     Task_App.o (.text.Task_Init)
                  00003fc4    000000f0     Motor.o (.text.Motor_SetDirc)
                  000040b4    000000f0     Task_App.o (.text.Task_Motor_PID)
                  000041a4    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00004290    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  00004374    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00004458    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  0000453c    000000e0     Task_App.o (.text.Task_OLED)
                  0000461c    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000046f8    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  000047d4    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000048ac    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004984    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004a58    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004b28    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004bec    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004cb0    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004d6c    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004e24    000000b4     Task.o (.text.Task_Add)
                  00004ed8    000000ac     Task_App.o (.text.Task_Serial)
                  00004f84    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00005030    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  000050dc    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00005186    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00005188    000000a2                            : udivmoddi4.S.obj (.text)
                  0000522a    00000002     --HOLE-- [fill = 0]
                  0000522c    000000a0     Motor.o (.text.Motor_SetDuty)
                  000052cc    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000536c    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00005408    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000054a0    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00005538    00000096     MPU6050.o (.text.inv_row_2_scale)
                  000055ce    00000002     --HOLE-- [fill = 0]
                  000055d0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  0000565c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000056e8    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005774    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  000057f8    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0000587c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000058fe    00000002     --HOLE-- [fill = 0]
                  00005900    00000080     Motor.o (.text.Motor_GetSpeed)
                  00005980    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000059fc    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005a70    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005ae4    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005b58    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00005bca    00000002     --HOLE-- [fill = 0]
                  00005bcc    00000070     Serial.o (.text.MyPrintf_DMA)
                  00005c3c    0000006e     OLED.o (.text.OLED_ShowString)
                  00005caa    00000002     --HOLE-- [fill = 0]
                  00005cac    0000006c     Motor.o (.text.Motor_Start)
                  00005d18    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00005d84    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005dee    00000002     --HOLE-- [fill = 0]
                  00005df0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005e58    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005ebe    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005f24    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005f88    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00005fec    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000604e    00000002     --HOLE-- [fill = 0]
                  00006050    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000060b2    00000002     --HOLE-- [fill = 0]
                  000060b4    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00006114    00000060     Key_Led.o (.text.Key_Read)
                  00006174    00000060     Task_App.o (.text.Task_IdleFunction)
                  000061d4    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00006234    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00006294    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  000062f4    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00006352    00000002     --HOLE-- [fill = 0]
                  00006354    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000063b0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000640c    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00006468    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  000064c4    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  0000651c    00000058     Serial.o (.text.Serial_Init)
                  00006574    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000065cc    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006624    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000667a    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000066cc    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  0000671c    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  0000676c    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000067bc    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00006808    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00006854    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000068a0    0000004c     OLED.o (.text.OLED_Printf)
                  000068ec    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00006938    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00006982    00000002     --HOLE-- [fill = 0]
                  00006984    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000069ce    00000002     --HOLE-- [fill = 0]
                  000069d0    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006a18    00000048     ADC.o (.text.adc_getValue)
                  00006a60    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006aa8    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006af0    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00006b38    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00006b7c    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00006bc0    00000044     Task_App.o (.text.Task_Key)
                  00006c04    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00006c48    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00006c8c    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00006cd0    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00006d12    00000002     --HOLE-- [fill = 0]
                  00006d14    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00006d56    00000002     --HOLE-- [fill = 0]
                  00006d58    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00006d98    00000040     Interrupt.o (.text.Interrupt_Init)
                  00006dd8    00000040     Task_App.o (.text.Task_GraySensor)
                  00006e18    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006e58    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00006e98    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00006ed8    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006f18    0000003e     Task.o (.text.Task_CMP)
                  00006f56    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00006f94    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006fd0    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000700c    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007048    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00007084    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000070c0    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  000070fc    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00007138    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00007174    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000071b0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000071ec    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00007226    00000002     --HOLE-- [fill = 0]
                  00007228    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00007262    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  0000729a    00000002     --HOLE-- [fill = 0]
                  0000729c    00000038     Task_App.o (.text.Task_LED)
                  000072d4    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  0000730c    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007340    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007374    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000073a8    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  000073dc    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  0000740e    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00007440    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00007470    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  000074a0    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  000074d0    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00007500    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00007530    00000030            : vsnprintf.c.obj (.text._outs)
                  00007560    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00007590    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  000075c0    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000075ec    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00007618    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00007644    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00007670    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  0000769a    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  000076c2    00000028     OLED.o (.text.DL_Common_updateReg)
                  000076ea    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00007712    00000002     --HOLE-- [fill = 0]
                  00007714    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  0000773c    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00007764    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  0000778c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000077b4    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  000077dc    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00007804    00000028     SysTick.o (.text.SysTick_Increasment)
                  0000782c    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00007854    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  0000787c    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  000078a2    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  000078c8    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000078ee    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007914    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00007938    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  0000795c    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00007980    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000079a2    00000002     --HOLE-- [fill = 0]
                  000079a4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000079c4    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000079e4    00000020     SysTick.o (.text.Delay)
                  00007a04    00000020     main.o (.text.main)
                  00007a24    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007a44    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007a62    00000002     --HOLE-- [fill = 0]
                  00007a64    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007a82    00000002     --HOLE-- [fill = 0]
                  00007a84    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00007aa0    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00007abc    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007ad8    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007af4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007b10    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007b2c    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007b48    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00007b64    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007b80    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007b9c    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007bb8    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00007bd4    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007bf0    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007c0c    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007c28    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00007c44    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007c60    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007c7c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007c98    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00007cb4    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007cd0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00007ce8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00007d00    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00007d18    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007d30    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00007d48    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007d60    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007d78    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007d90    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007da8    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007dc0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00007dd8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00007df0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00007e08    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00007e20    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00007e38    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00007e50    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00007e68    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00007e80    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007e98    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00007eb0    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007ec8    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007ee0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00007ef8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00007f10    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00007f28    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00007f40    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00007f58    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00007f70    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00007f88    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00007fa0    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00007fb8    00000018     OLED.o (.text.DL_I2C_reset)
                  00007fd0    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007fe8    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00008000    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00008018    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00008030    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00008048    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00008060    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00008078    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00008090    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000080a8    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  000080c0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000080d8    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  000080f0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00008108    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00008120    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00008138    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00008150    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00008168    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00008180    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00008198    00000018            : vsprintf.c.obj (.text._outs)
                  000081b0    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  000081c6    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  000081dc    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000081f2    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00008208    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  0000821e    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00008234    00000016     OLED.o (.text.DL_GPIO_readPins)
                  0000824a    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00008260    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00008276    00000016     SysTick.o (.text.SysGetTick)
                  0000828c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000082a2    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  000082b6    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  000082ca    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  000082de    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000082f2    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00008306    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000831a    00000002     --HOLE-- [fill = 0]
                  0000831c    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00008330    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00008344    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00008358    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  0000836c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00008380    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00008394    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000083a8    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000083bc    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  000083d0    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  000083e4    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000083f8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000840a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000841c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000842e    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  0000843e    00000002     --HOLE-- [fill = 0]
                  00008440    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00008450    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00008460    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00008470    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00008480    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  0000848e    00000002     --HOLE-- [fill = 0]
                  00008490    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000849e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000084ac    0000000e     MPU6050.o (.text.tap_cb)
                  000084ba    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  000084c8    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000084d4    0000000c     SysTick.o (.text.Sys_GetTick)
                  000084e0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000084ea    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000084f4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00008504    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000850e    00000002     --HOLE-- [fill = 0]
                  00008510    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00008520    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000852a    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008534    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000853e    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00008548    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00008558    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  00008562    0000000a     MPU6050.o (.text.android_orient_cb)
                  0000856c    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00008574    00000008     Interrupt.o (.text.SysTick_Handler)
                  0000857c    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00008584    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000858c    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008592    00000002     --HOLE-- [fill = 0]
                  00008594    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  000085a4    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  000085aa    00000006            : exit.c.obj (.text:abort)
                  000085b0    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000085b4    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000085b8    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  000085bc    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000085c0    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000085d0    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000085d4    0000000c     --HOLE-- [fill = 0]

.cinit     0    00009cd0    00000078     
                  00009cd0    0000004e     (.cinit..data.load) [load image, compression = lzss]
                  00009d1e    00000002     --HOLE-- [fill = 0]
                  00009d20    0000000c     (__TI_handler_table)
                  00009d2c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00009d34    00000010     (__TI_cinit_table)
                  00009d44    00000004     --HOLE-- [fill = 0]

.rodata    0    000085e0    000016f0     
                  000085e0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  000091d6    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  000097c6    00000228     OLED_Font.o (.rodata.asc2_0806)
                  000099ee    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000099f0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009af1    00000007     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00009af8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00009b38    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00009b60    00000028     inv_mpu.o (.rodata.test)
                  00009b88    0000001f     Task_App.o (.rodata.str1.7950429023856218820.1)
                  00009ba7    0000001e     inv_mpu.o (.rodata.reg)
                  00009bc5    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  00009bc8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00009be0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00009bf8    00000014     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00009c0c    00000014     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00009c20    00000014     Task_App.o (.rodata.str1.5883415095785080416.1)
                  00009c34    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00009c45    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00009c56    00000011     Task_App.o (.rodata.str1.4769078833470683459.1)
                  00009c67    00000001     --HOLE-- [fill = 0]
                  00009c68    0000000c     inv_mpu.o (.rodata.hw)
                  00009c74    0000000c     Task_App.o (.rodata.str1.3850258909703972507.1)
                  00009c80    0000000b     Task_App.o (.rodata.str1.492715258893803702.1)
                  00009c8b    00000001     --HOLE-- [fill = 0]
                  00009c8c    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00009c96    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00009c98    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00009ca0    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  00009ca8    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00009cb0    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00009cb6    00000005     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00009cbb    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00009cbf    00000004     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00009cc3    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00009cc5    0000000b     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    00000133     UNINITIALIZED
                  202003d4    00000048     Motor.o (.data.Motor_Left)
                  2020041c    00000048     Motor.o (.data.Motor_Right)
                  20200464    0000002c     inv_mpu.o (.data.st)
                  20200490    00000010     Task_App.o (.data.Gray_Anolog)
                  202004a0    00000010     Task_App.o (.data.Gray_Normal)
                  202004b0    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004c0    0000000e     MPU6050.o (.data.hal)
                  202004ce    00000009     MPU6050.o (.data.gyro_orientation)
                  202004d7    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004df    00000001     Task_App.o (.data.Flag_LED)
                  202004e0    00000008     Task_App.o (.data.Motor)
                  202004e8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004ec    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004f0    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004f4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004f8    00000004     SysTick.o (.data.delayTick)
                  202004fc    00000004     SysTick.o (.data.uwTick)
                  20200500    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  20200502    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  20200503    00000001     Task_App.o (.data.Gray_Digtal)
                  20200504    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  20200505    00000001     Task.o (.data.Task_Num)
                  20200506    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3426    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3466    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       1512    165       241    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1982    165       247    
                                                                 
    .\BSP\Src\
       MPU6050.o                        2468    0         70     
       OLED_Font.o                      0       2072      0      
       OLED.o                           1854    0         0      
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Serial.o                         404     0         512    
       Task.o                           674     0         241    
       Motor.o                          704     0         144    
       PID_IQMath.o                     402     0         0      
       ADC.o                            236     0         0      
       Key_Led.o                        118     0         0      
       SysTick.o                        106     0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8210    2072      975    
                                                                 
    .\DMP\
       inv_mpu_dmp_motion_driver.o      3110    3062      16     
       inv_mpu.o                        4600    82        44     
    +--+--------------------------------+-------+---------+---------+
       Total:                           7710    3144      60     
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1176    0         0      
                                                                 
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           96      0         0      
                                                                 
    E:\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       vsprintf.c.obj                   78      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       memcmp.c.obj                     32      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8356    355       4      
                                                                 
    E:\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    E:\ccs\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3020    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       114       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     34020   6165      1798   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00009d34 records: 2, size/record: 8, table size: 16
	.data: load addr=00009cd0, load size=0000004e bytes, run addr=202003d4, run size=00000133 bytes, compression=lzss
	.bss: load addr=00009d2c, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00009d20 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002515     000084f4     000084f2   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00004375     00008510     0000850c   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00008528          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             0000853c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00008572          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             000085a8          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003ab1     00008548     00008546   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   0000251f     00008594     00008590   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             000085ba          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00007855     000085c0     000085bc   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000085b1  ADC0_IRQHandler                      
000085b1  ADC1_IRQHandler                      
000085b1  AES_IRQHandler                       
000085b4  C$$EXIT                              
000085b1  CANFD0_IRQHandler                    
000085b1  DAC0_IRQHandler                      
00006d59  DL_ADC12_setClockConfig              
000084e1  DL_Common_delayCycles                
00006809  DL_DMA_initChannel                   
000062f5  DL_I2C_fillControllerTXFIFO          
00007049  DL_I2C_flushControllerTXFIFO         
000078ef  DL_I2C_setClockConfig                
0000461d  DL_SYSCTL_configSYSPLL               
00005f25  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006b39  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003cc5  DL_Timer_initFourCCPWMMode           
00007c61  DL_Timer_setCaptCompUpdateMethod     
00008079  DL_Timer_setCaptureCompareOutCtl     
00008451  DL_Timer_setCaptureCompareValue      
00007c7d  DL_Timer_setClockConfig              
000069d1  DL_UART_init                         
000083f9  DL_UART_setClockConfig               
000085b1  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202004d7  Data_Tracker_Input                   
202004f0  Data_Tracker_Offset                  
202003c4  Data_Yaw                             
000085b1  Default_Handler                      
000079e5  Delay                                
202003c8  ExISR_Flag                           
202004df  Flag_LED                             
20200502  Flag_MPU6050_Ready                   
000085b1  GROUP0_IRQHandler                    
00004291  GROUP1_IRQHandler                    
000046f9  Get_Analog_value                     
000070c1  Get_Anolog_Value                     
00008481  Get_Digtal_For_User                  
00007263  Get_Normalize_For_User               
202002f0  GraySensor                           
20200490  Gray_Anolog                          
20200503  Gray_Digtal                          
202004a0  Gray_Normal                          
000085b5  HOSTexit                             
000085b1  HardFault_Handler                    
000085b1  I2C0_IRQHandler                      
000085b1  I2C1_IRQHandler                      
00005d85  I2C_OLED_Clear                       
000070fd  I2C_OLED_Set_Pos                     
00005409  I2C_OLED_WR_Byte                     
000060b5  I2C_OLED_i2c_sda_unlock              
00006d99  Interrupt_Init                       
00006115  Key_Read                             
00002ca1  MPU6050_Init                         
202004e0  Motor                                
00005901  Motor_GetSpeed                       
202003d4  Motor_Left                           
2020041c  Motor_Right                          
0000522d  Motor_SetDuty                        
00005cad  Motor_Start                          
00005bcd  MyPrintf_DMA                         
000085b1  NMI_Handler                          
000026a9  No_MCU_Ganv_Sensor_Init              
00005b59  No_MCU_Ganv_Sensor_Init_Frist        
00006cd1  No_Mcu_Ganv_Sensor_Task_Without_tick 
000039a1  OLED_Init                            
000068a1  OLED_Printf                          
00003189  OLED_ShowChar                        
00005c3d  OLED_ShowString                      
00007671  PID_IQ_Init                          
00003639  PID_IQ_Prosc                         
00006b7d  PID_IQ_SetParams                     
000085b1  PendSV_Handler                       
000085b1  RTC_IRQHandler                       
0000159d  Read_Quad                            
000085bd  Reset_Handler                        
000085b1  SPI0_IRQHandler                      
000085b1  SPI1_IRQHandler                      
000085b1  SVC_Handler                          
000068ed  SYSCFG_DL_ADC1_init                  
000074a1  SYSCFG_DL_DMA_CH_RX_init             
00008139  SYSCFG_DL_DMA_CH_TX_init             
000084c9  SYSCFG_DL_DMA_init                   
00001e09  SYSCFG_DL_GPIO_init                  
000064c5  SYSCFG_DL_I2C_MPU6050_init           
00005f89  SYSCFG_DL_I2C_OLED_init              
000055d1  SYSCFG_DL_Motor_PWM_init             
00006355  SYSCFG_DL_SYSCTL_init                
00008461  SYSCFG_DL_SYSTICK_init               
00005775  SYSCFG_DL_UART0_init                 
000075c1  SYSCFG_DL_init                       
000052cd  SYSCFG_DL_initPower                  
0000651d  Serial_Init                          
20200000  Serial_RxData                        
00008277  SysGetTick                           
00008575  SysTick_Handler                      
00007805  SysTick_Increasment                  
000084d5  Sys_GetTick                          
000085b1  TIMA0_IRQHandler                     
000085b1  TIMA1_IRQHandler                     
000085b1  TIMG0_IRQHandler                     
000085b1  TIMG12_IRQHandler                    
000085b1  TIMG6_IRQHandler                     
000085b1  TIMG7_IRQHandler                     
000085b1  TIMG8_IRQHandler                     
0000840b  TI_memcpy_small                      
000084bb  TI_memset_small                      
00004e25  Task_Add                             
00006dd9  Task_GraySensor                      
00006175  Task_IdleFunction                    
00003ec9  Task_Init                            
00006bc1  Task_Key                             
0000729d  Task_LED                             
000040b5  Task_Motor_PID                       
0000453d  Task_OLED                            
00004ed9  Task_Serial                          
000021c5  Task_Start                           
000033e9  Task_Tracker                         
000085b1  UART0_IRQHandler                     
000085b1  UART1_IRQHandler                     
000085b1  UART2_IRQHandler                     
000085b1  UART3_IRQHandler                     
00008151  _IQ24div                             
00008169  _IQ24mpy                             
000074d1  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00009d34  __TI_CINIT_Base                      
00009d44  __TI_CINIT_Limit                     
00009d44  __TI_CINIT_Warm                      
00009d20  __TI_Handler_Table_Base              
00009d2c  __TI_Handler_Table_Limit             
000071b1  __TI_auto_init_nobinit_nopinit       
00005981  __TI_decompress_lzss                 
0000841d  __TI_decompress_none                 
00006575  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000828d  __TI_zero_init_nomemset              
0000251f  __adddf3                             
000048b7  __addsf3                             
000099f0  __aeabi_ctype_table_                 
000099f0  __aeabi_ctype_table_C                
00005a71  __aeabi_d2f                          
00006985  __aeabi_d2iz                         
00006d15  __aeabi_d2uiz                        
0000251f  __aeabi_dadd                         
00005fed  __aeabi_dcmpeq                       
00006029  __aeabi_dcmpge                       
0000603d  __aeabi_dcmpgt                       
00006015  __aeabi_dcmple                       
00006001  __aeabi_dcmplt                       
00003ab1  __aeabi_ddiv                         
00004375  __aeabi_dmul                         
00002515  __aeabi_dsub                         
202004f4  __aeabi_errno                        
0000857d  __aeabi_errno_addr                   
00006e59  __aeabi_f2d                          
000072d5  __aeabi_f2iz                         
000048b7  __aeabi_fadd                         
00006051  __aeabi_fcmpeq                       
0000608d  __aeabi_fcmpge                       
000060a1  __aeabi_fcmpgt                       
00006079  __aeabi_fcmple                       
00006065  __aeabi_fcmplt                       
0000587d  __aeabi_fdiv                         
0000565d  __aeabi_fmul                         
000048ad  __aeabi_fsub                         
00007619  __aeabi_i2d                          
00007139  __aeabi_i2f                          
00006625  __aeabi_idiv                         
000026a7  __aeabi_idiv0                        
00006625  __aeabi_idivmod                      
00005187  __aeabi_ldiv0                        
00007a65  __aeabi_llsl                         
0000795d  __aeabi_lmul                         
00008585  __aeabi_memcpy                       
00008585  __aeabi_memcpy4                      
00008585  __aeabi_memcpy8                      
00008491  __aeabi_memset                       
00008491  __aeabi_memset4                      
00008491  __aeabi_memset8                      
00007939  __aeabi_ui2d                         
0000782d  __aeabi_ui2f                         
00006e19  __aeabi_uidiv                        
00006e19  __aeabi_uidivmod                     
000083a9  __aeabi_uldivmod                     
00007a65  __ashldi3                            
ffffffff  __binit__                            
00005df1  __cmpdf2                             
000071ed  __cmpsf2                             
00003ab1  __divdf3                             
0000587d  __divsf3                             
00005df1  __eqdf2                              
000071ed  __eqsf2                              
00006e59  __extendsfdf2                        
00006985  __fixdfsi                            
000072d5  __fixsfsi                            
00006d15  __fixunsdfsi                         
00007619  __floatsidf                          
00007139  __floatsisf                          
00007939  __floatunsidf                        
0000782d  __floatunsisf                        
000059fd  __gedf2                              
00007175  __gesf2                              
000059fd  __gtdf2                              
00007175  __gtsf2                              
00005df1  __ledf2                              
000071ed  __lesf2                              
00005df1  __ltdf2                              
000071ed  __ltsf2                              
UNDEFED   __mpu_init                           
00004375  __muldf3                             
0000795d  __muldi3                             
00007229  __muldsi3                            
0000565d  __mulsf3                             
00005df1  __nedf2                              
000071ed  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002515  __subdf3                             
000048ad  __subsf3                             
00005a71  __truncdfsf2                         
00005189  __udivmoddi4                         
00007855  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000085d1  _system_pre_init                     
000085ab  abort                                
00006a19  adc_getValue                         
000097c6  asc2_0806                            
000091d6  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002831  atan2                                
00002831  atan2l                               
00000df5  atanl                                
00006e99  atoi                                 
ffffffff  binit                                
00005d19  convertAnalogToDigital               
202004f8  delayTick                            
00006a61  dmp_enable_6x_lp_quat                
000010ed  dmp_enable_feature                   
000061d5  dmp_enable_gyro_cal                  
00006aa9  dmp_enable_lp_quat                   
00007cb5  dmp_load_motion_driver_firmware      
00001c15  dmp_read_fifo                        
000083bd  dmp_register_android_orient_cb       
000083d1  dmp_register_tap_cb                  
000054a1  dmp_set_fifo_rate                    
000029b9  dmp_set_orientation                  
00006c05  dmp_set_shake_reject_thresh          
000073dd  dmp_set_shake_reject_time            
0000740f  dmp_set_shake_reject_timeout         
00005ebf  dmp_set_tap_axes                     
00006c49  dmp_set_tap_count                    
00001365  dmp_set_tap_thresh                   
00007561  dmp_set_tap_time                     
00007591  dmp_set_tap_time_multi               
20200506  enable_group1_irq                    
000063b1  frexp                                
000063b1  frexpl                               
00009c68  hw                                   
00000000  interruptVectors                     
000047d5  ldexp                                
000047d5  ldexpl                               
00007a05  main                                 
00007981  memccpy                              
00007a25  memcmp                               
202003d2  more                                 
00006235  mpu6050_i2c_sda_unlock               
00004cb1  mpu_configure_fifo                   
00005ae5  mpu_get_accel_fsr                    
00006295  mpu_get_gyro_fsr                     
000073a9  mpu_get_sample_rate                  
00003511  mpu_init                             
0000375d  mpu_load_firmware                    
00003dc9  mpu_lp_accel_mode                    
00003bbd  mpu_read_fifo_stream                 
00004f85  mpu_read_mem                         
000017c9  mpu_reset_fifo                       
00004459  mpu_set_accel_fsr                    
00002375  mpu_set_bypass                       
00004d6d  mpu_set_dmp_state                    
00004b29  mpu_set_gyro_fsr                     
0000536d  mpu_set_int_latched                  
00004a59  mpu_set_lpf                          
000041a5  mpu_set_sample_rate                  
000032b9  mpu_set_sensors                      
00005031  mpu_write_mem                        
00002f21  mspm0_i2c_read                       
00004bed  mspm0_i2c_write                      
000050dd  normalizeAnalogValues                
00003055  qsort                                
202003a0  quat                                 
00009ba7  reg                                  
000047d5  scalbn                               
000047d5  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
00002b31  sqrt                                 
00002b31  sqrtl                                
00009b60  test                                 
202004fc  uwTick                               
00006ed9  vsnprintf                            
00007645  vsprintf                             
00008471  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  dmp_enable_feature                   
00001365  dmp_set_tap_thresh                   
0000159d  Read_Quad                            
000017c9  mpu_reset_fifo                       
00001c15  dmp_read_fifo                        
00001e09  SYSCFG_DL_GPIO_init                  
000021c5  Task_Start                           
00002375  mpu_set_bypass                       
00002515  __aeabi_dsub                         
00002515  __subdf3                             
0000251f  __adddf3                             
0000251f  __aeabi_dadd                         
000026a7  __aeabi_idiv0                        
000026a9  No_MCU_Ganv_Sensor_Init              
00002831  atan2                                
00002831  atan2l                               
000029b9  dmp_set_orientation                  
00002b31  sqrt                                 
00002b31  sqrtl                                
00002ca1  MPU6050_Init                         
00002f21  mspm0_i2c_read                       
00003055  qsort                                
00003189  OLED_ShowChar                        
000032b9  mpu_set_sensors                      
000033e9  Task_Tracker                         
00003511  mpu_init                             
00003639  PID_IQ_Prosc                         
0000375d  mpu_load_firmware                    
000039a1  OLED_Init                            
00003ab1  __aeabi_ddiv                         
00003ab1  __divdf3                             
00003bbd  mpu_read_fifo_stream                 
00003cc5  DL_Timer_initFourCCPWMMode           
00003dc9  mpu_lp_accel_mode                    
00003ec9  Task_Init                            
000040b5  Task_Motor_PID                       
000041a5  mpu_set_sample_rate                  
00004291  GROUP1_IRQHandler                    
00004375  __aeabi_dmul                         
00004375  __muldf3                             
00004459  mpu_set_accel_fsr                    
0000453d  Task_OLED                            
0000461d  DL_SYSCTL_configSYSPLL               
000046f9  Get_Analog_value                     
000047d5  ldexp                                
000047d5  ldexpl                               
000047d5  scalbn                               
000047d5  scalbnl                              
000048ad  __aeabi_fsub                         
000048ad  __subsf3                             
000048b7  __addsf3                             
000048b7  __aeabi_fadd                         
00004a59  mpu_set_lpf                          
00004b29  mpu_set_gyro_fsr                     
00004bed  mspm0_i2c_write                      
00004cb1  mpu_configure_fifo                   
00004d6d  mpu_set_dmp_state                    
00004e25  Task_Add                             
00004ed9  Task_Serial                          
00004f85  mpu_read_mem                         
00005031  mpu_write_mem                        
000050dd  normalizeAnalogValues                
00005187  __aeabi_ldiv0                        
00005189  __udivmoddi4                         
0000522d  Motor_SetDuty                        
000052cd  SYSCFG_DL_initPower                  
0000536d  mpu_set_int_latched                  
00005409  I2C_OLED_WR_Byte                     
000054a1  dmp_set_fifo_rate                    
000055d1  SYSCFG_DL_Motor_PWM_init             
0000565d  __aeabi_fmul                         
0000565d  __mulsf3                             
00005775  SYSCFG_DL_UART0_init                 
0000587d  __aeabi_fdiv                         
0000587d  __divsf3                             
00005901  Motor_GetSpeed                       
00005981  __TI_decompress_lzss                 
000059fd  __gedf2                              
000059fd  __gtdf2                              
00005a71  __aeabi_d2f                          
00005a71  __truncdfsf2                         
00005ae5  mpu_get_accel_fsr                    
00005b59  No_MCU_Ganv_Sensor_Init_Frist        
00005bcd  MyPrintf_DMA                         
00005c3d  OLED_ShowString                      
00005cad  Motor_Start                          
00005d19  convertAnalogToDigital               
00005d85  I2C_OLED_Clear                       
00005df1  __cmpdf2                             
00005df1  __eqdf2                              
00005df1  __ledf2                              
00005df1  __ltdf2                              
00005df1  __nedf2                              
00005ebf  dmp_set_tap_axes                     
00005f25  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005f89  SYSCFG_DL_I2C_OLED_init              
00005fed  __aeabi_dcmpeq                       
00006001  __aeabi_dcmplt                       
00006015  __aeabi_dcmple                       
00006029  __aeabi_dcmpge                       
0000603d  __aeabi_dcmpgt                       
00006051  __aeabi_fcmpeq                       
00006065  __aeabi_fcmplt                       
00006079  __aeabi_fcmple                       
0000608d  __aeabi_fcmpge                       
000060a1  __aeabi_fcmpgt                       
000060b5  I2C_OLED_i2c_sda_unlock              
00006115  Key_Read                             
00006175  Task_IdleFunction                    
000061d5  dmp_enable_gyro_cal                  
00006235  mpu6050_i2c_sda_unlock               
00006295  mpu_get_gyro_fsr                     
000062f5  DL_I2C_fillControllerTXFIFO          
00006355  SYSCFG_DL_SYSCTL_init                
000063b1  frexp                                
000063b1  frexpl                               
000064c5  SYSCFG_DL_I2C_MPU6050_init           
0000651d  Serial_Init                          
00006575  __TI_ltoa                            
00006625  __aeabi_idiv                         
00006625  __aeabi_idivmod                      
00006809  DL_DMA_initChannel                   
000068a1  OLED_Printf                          
000068ed  SYSCFG_DL_ADC1_init                  
00006985  __aeabi_d2iz                         
00006985  __fixdfsi                            
000069d1  DL_UART_init                         
00006a19  adc_getValue                         
00006a61  dmp_enable_6x_lp_quat                
00006aa9  dmp_enable_lp_quat                   
00006b39  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006b7d  PID_IQ_SetParams                     
00006bc1  Task_Key                             
00006c05  dmp_set_shake_reject_thresh          
00006c49  dmp_set_tap_count                    
00006cd1  No_Mcu_Ganv_Sensor_Task_Without_tick 
00006d15  __aeabi_d2uiz                        
00006d15  __fixunsdfsi                         
00006d59  DL_ADC12_setClockConfig              
00006d99  Interrupt_Init                       
00006dd9  Task_GraySensor                      
00006e19  __aeabi_uidiv                        
00006e19  __aeabi_uidivmod                     
00006e59  __aeabi_f2d                          
00006e59  __extendsfdf2                        
00006e99  atoi                                 
00006ed9  vsnprintf                            
00007049  DL_I2C_flushControllerTXFIFO         
000070c1  Get_Anolog_Value                     
000070fd  I2C_OLED_Set_Pos                     
00007139  __aeabi_i2f                          
00007139  __floatsisf                          
00007175  __gesf2                              
00007175  __gtsf2                              
000071b1  __TI_auto_init_nobinit_nopinit       
000071ed  __cmpsf2                             
000071ed  __eqsf2                              
000071ed  __lesf2                              
000071ed  __ltsf2                              
000071ed  __nesf2                              
00007229  __muldsi3                            
00007263  Get_Normalize_For_User               
0000729d  Task_LED                             
000072d5  __aeabi_f2iz                         
000072d5  __fixsfsi                            
000073a9  mpu_get_sample_rate                  
000073dd  dmp_set_shake_reject_time            
0000740f  dmp_set_shake_reject_timeout         
000074a1  SYSCFG_DL_DMA_CH_RX_init             
000074d1  _IQ24toF                             
00007561  dmp_set_tap_time                     
00007591  dmp_set_tap_time_multi               
000075c1  SYSCFG_DL_init                       
00007619  __aeabi_i2d                          
00007619  __floatsidf                          
00007645  vsprintf                             
00007671  PID_IQ_Init                          
00007805  SysTick_Increasment                  
0000782d  __aeabi_ui2f                         
0000782d  __floatunsisf                        
00007855  _c_int00_noargs                      
000078ef  DL_I2C_setClockConfig                
00007939  __aeabi_ui2d                         
00007939  __floatunsidf                        
0000795d  __aeabi_lmul                         
0000795d  __muldi3                             
00007981  memccpy                              
000079e5  Delay                                
00007a05  main                                 
00007a25  memcmp                               
00007a65  __aeabi_llsl                         
00007a65  __ashldi3                            
00007c61  DL_Timer_setCaptCompUpdateMethod     
00007c7d  DL_Timer_setClockConfig              
00007cb5  dmp_load_motion_driver_firmware      
00008079  DL_Timer_setCaptureCompareOutCtl     
00008139  SYSCFG_DL_DMA_CH_TX_init             
00008151  _IQ24div                             
00008169  _IQ24mpy                             
00008277  SysGetTick                           
0000828d  __TI_zero_init_nomemset              
000083a9  __aeabi_uldivmod                     
000083bd  dmp_register_android_orient_cb       
000083d1  dmp_register_tap_cb                  
000083f9  DL_UART_setClockConfig               
0000840b  TI_memcpy_small                      
0000841d  __TI_decompress_none                 
00008451  DL_Timer_setCaptureCompareValue      
00008461  SYSCFG_DL_SYSTICK_init               
00008471  wcslen                               
00008481  Get_Digtal_For_User                  
00008491  __aeabi_memset                       
00008491  __aeabi_memset4                      
00008491  __aeabi_memset8                      
000084bb  TI_memset_small                      
000084c9  SYSCFG_DL_DMA_init                   
000084d5  Sys_GetTick                          
000084e1  DL_Common_delayCycles                
00008575  SysTick_Handler                      
0000857d  __aeabi_errno_addr                   
00008585  __aeabi_memcpy                       
00008585  __aeabi_memcpy4                      
00008585  __aeabi_memcpy8                      
000085ab  abort                                
000085b1  ADC0_IRQHandler                      
000085b1  ADC1_IRQHandler                      
000085b1  AES_IRQHandler                       
000085b1  CANFD0_IRQHandler                    
000085b1  DAC0_IRQHandler                      
000085b1  DMA_IRQHandler                       
000085b1  Default_Handler                      
000085b1  GROUP0_IRQHandler                    
000085b1  HardFault_Handler                    
000085b1  I2C0_IRQHandler                      
000085b1  I2C1_IRQHandler                      
000085b1  NMI_Handler                          
000085b1  PendSV_Handler                       
000085b1  RTC_IRQHandler                       
000085b1  SPI0_IRQHandler                      
000085b1  SPI1_IRQHandler                      
000085b1  SVC_Handler                          
000085b1  TIMA0_IRQHandler                     
000085b1  TIMA1_IRQHandler                     
000085b1  TIMG0_IRQHandler                     
000085b1  TIMG12_IRQHandler                    
000085b1  TIMG6_IRQHandler                     
000085b1  TIMG7_IRQHandler                     
000085b1  TIMG8_IRQHandler                     
000085b1  UART0_IRQHandler                     
000085b1  UART1_IRQHandler                     
000085b1  UART2_IRQHandler                     
000085b1  UART3_IRQHandler                     
000085b4  C$$EXIT                              
000085b5  HOSTexit                             
000085bd  Reset_Handler                        
000085d1  _system_pre_init                     
000091d6  asc2_1608                            
000097c6  asc2_0806                            
000099f0  __aeabi_ctype_table_                 
000099f0  __aeabi_ctype_table_C                
00009b60  test                                 
00009ba7  reg                                  
00009c68  hw                                   
00009d20  __TI_Handler_Table_Base              
00009d2c  __TI_Handler_Table_Limit             
00009d34  __TI_CINIT_Base                      
00009d44  __TI_CINIT_Limit                     
00009d44  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
202003d4  Motor_Left                           
2020041c  Motor_Right                          
20200490  Gray_Anolog                          
202004a0  Gray_Normal                          
202004d7  Data_Tracker_Input                   
202004df  Flag_LED                             
202004e0  Motor                                
202004e8  Data_MotorEncoder                    
202004ec  Data_Motor_TarSpeed                  
202004f0  Data_Tracker_Offset                  
202004f4  __aeabi_errno                        
202004f8  delayTick                            
202004fc  uwTick                               
20200502  Flag_MPU6050_Ready                   
20200503  Gray_Digtal                          
20200506  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[326 symbols]
