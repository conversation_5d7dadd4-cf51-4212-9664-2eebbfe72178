# 智能小车电机低速运行功能说明

## 功能概述

修改后的智能小车系统在上电时会自动让左右两个电机维持低转速运行，并在OLED屏幕上实时显示电机速度信息。

## 主要修改内容

### 1. 电机初始化修改 (Motor.c)

**修改位置**: `Motor_Start()` 函数
- 设置初始目标速度为10（左右电机）
- 设置初始PWM占空比为15%，确保电机立即开始转动
- 保持原有的PID参数设置

### 2. 目标速度调整 (Task_App.c)

**修改位置**: `Data_Motor_TarSpeed` 变量
- 将初始目标速度从30降低到10
- 确保上电时电机以低速运行

### 3. OLED显示优化 (Task_App.c)

**修改位置**: `Task_OLED()` 函数
- **第1行**: 显示左右电机实时速度 `L:XX.X R:XX.X`
- **第2行**: 显示目标速度和循迹偏差 `Tar:XX.X Off:XX.X`
- **第3行**: 显示姿态角度 `P:XX.X R:XX.X Y:XX.X`
- **第4行**: 显示系统运行时间 `Time:XXXXus`

### 4. 按键控制功能增强 (Task_App.c)

**修改位置**: `Task_Key()` 函数
- **S2按键**: 增加目标速度（每次+5，最大50）
- **K1按键**: 减少目标速度（每次-5，最小0）
- **K2按键**: 重置为初始低速（10）
- 按键操作时LED指示：红灯=加速，蓝灯=减速，板载LED=重置

### 5. 新增函数

**Motor.h/Motor.c**: 添加 `Motor_SetTargetSpeed()` 函数
- 用于动态设置单个电机的目标速度
- 便于后续功能扩展

## 系统运行流程

1. **上电初始化**:
   - 系统初始化各个模块
   - 电机自动设置为目标速度10，PWM占空比15%
   - OLED开始显示实时数据

2. **运行状态**:
   - 电机PID控制器每50ms调节一次
   - OLED每50ms更新显示内容
   - 按键每20ms检测一次

3. **用户交互**:
   - 通过按键可以实时调整电机速度
   - OLED实时反馈速度变化
   - LED指示当前操作状态

## OLED显示内容详解

```
L:10.5 R:10.3    <- 左右电机实际速度
Tar:10.0 Off:0.0 <- 目标速度和循迹偏差
P:0.1 R:-0.2 Y:0.5 <- 俯仰角/横滚角/偏航角
Time:1234us      <- 系统运行时间(秒)
```

## 技术参数

- **初始目标速度**: 10 (编码器脉冲/秒)
- **初始PWM占空比**: 15%
- **速度调节步长**: 5
- **速度范围**: 0-50
- **显示更新频率**: 20Hz (50ms)
- **按键检测频率**: 50Hz (20ms)

## 注意事项

1. 电机速度单位基于编码器脉冲计数，具体的物理速度需要根据编码器分辨率和轮径计算
2. PID参数已预设为合适值，如需调整请修改 `Motor_Start()` 中的参数
3. 系统支持循迹功能，电机速度会根据循迹偏差自动调整
4. 建议在安全环境下测试，确保小车有足够的运行空间

## 故障排除

- **电机不转**: 检查PWM信号和电机驱动器连接
- **速度显示异常**: 检查编码器连接和中断配置
- **OLED无显示**: 检查I2C连接和电源
- **按键无响应**: 检查按键硬件连接和上拉电阻
