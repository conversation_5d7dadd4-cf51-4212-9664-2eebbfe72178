# 天猛星开发板按键查找和测试指南

## 问题说明

您提到找不到S2、K1、K2这三个按键。这可能是因为：
1. 按键的实际标识与代码中的名称不同
2. 按键位置不明显
3. 开发板版本差异

## 按键配置信息

根据代码分析，系统配置了以下按键：

| 代码名称 | GPIO引脚 | 物理引脚 | 预期功能 |
|---------|----------|----------|----------|
| S2 | GPIOB.21 | Pin 20 | 速度增加 |
| K1 | GPIOB.19 | Pin 16 | 速度减少 |
| K2 | GPIOB.20 | Pin 19 | 速度重置 |

## 按键查找方法

### 1. 视觉查找
在天猛星开发板上寻找以下可能的按键标识：
- **SW1、SW2、SW3** (开关按键)
- **BTN1、BTN2、BTN3** (按钮)
- **USER、RESET、BOOT** (功能按键)
- **KEY1、KEY2、KEY3** (按键)
- **S1、S2、S3** (开关)
- **K1、K2、K3** (按键)
- 没有标识的小型轻触开关

### 2. 物理位置查找
按键通常位于开发板的以下位置：
- 开发板边缘
- 芯片附近
- LED指示灯附近
- 电源接口附近
- USB接口附近

### 3. 按键测试方法

#### 方法一：LED指示测试
1. 上电运行程序
2. 按下开发板上的任意按键
3. 观察板载LED是否闪烁
4. 如果LED闪烁，说明按键被检测到

#### 方法二：OLED显示测试
1. 观察OLED显示的目标速度值
2. 按下不同按键
3. 观察目标速度是否发生变化：
   - 速度增加5 → 对应"增加速度"按键
   - 速度减少5 → 对应"减少速度"按键  
   - 速度重置为10 → 对应"重置速度"按键

#### 方法三：串口调试测试
如果有串口连接，可以在按键函数中添加调试输出：

```c
if (Key_Val)
{
    // 添加调试输出
    printf("Key pressed: %d\n", Key_Val);
    // 原有代码...
}
```

## 常见按键值对应关系

根据代码逻辑，按键值的含义：
- **Key_Val = 1** → 第一个按键被按下
- **Key_Val = 2** → 第二个按键被按下  
- **Key_Val = 4** → 第三个按键被按下
- **其他值** → 多个按键同时按下

## 如果找不到按键的解决方案

### 方案1：使用现有按键
如果开发板上只有1-2个按键，可以修改代码：

```c
switch(Key_Val)
{
    case 1: // 唯一按键：循环切换速度
        static uint8_t speed_level = 1;
        speed_level = (speed_level % 5) + 1; // 1-5循环
        Data_Motor_TarSpeed = _IQ(speed_level * 10); // 10,20,30,40,50
        break;
}
```

### 方案2：使用外部按键
如果需要更多按键，可以：
1. 连接外部按键到指定GPIO引脚
2. 使用杜邦线连接轻触开关
3. 连接到面包板上的按键

### 方案3：使用串口控制
通过串口发送命令控制速度：
- 发送 '+' 增加速度
- 发送 '-' 减少速度
- 发送 'r' 重置速度

## 硬件连接说明

如果需要外接按键，连接方式：
```
按键 → GPIO引脚 → GND
     ↑
   上拉电阻(10kΩ)
     ↑
   3.3V
```

## 调试建议

1. **先确认基本功能**：确保电机能正常运行，OLED能正常显示
2. **逐个测试按键**：按下每个可能的按键，观察系统响应
3. **查看原理图**：如果有开发板原理图，查看按键的具体连接
4. **联系厂商**：向立创开发板团队咨询具体的按键位置

## 修改后的按键功能

当前代码已修改为：
- **任意按键** → 板载LED切换状态（确认按键响应）
- **按键值1** → 速度+5，红灯闪烁
- **按键值2** → 速度-5，蓝灯闪烁  
- **按键值4** → 速度重置为10，红蓝灯同时闪烁
- **其他按键** → 停止电机（速度设为0）

## 总结

不用担心找不到具体的S2、K1、K2标识，重要的是：
1. 找到开发板上实际存在的按键
2. 通过测试确定按键的功能
3. 根据需要调整代码中的按键响应逻辑

按键的具体标识不重要，重要的是功能能够正常实现。
